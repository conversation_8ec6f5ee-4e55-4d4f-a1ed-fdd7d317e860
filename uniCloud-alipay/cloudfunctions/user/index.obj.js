const { generateToken, verifyToken } = require("./jwt-utils");

const expiresIn = 7 * 24 * 60 * 60;

// 私有方法：通过微信code获取openId
async function getWxOpenId(code, appId, appSecret) {
  try {
    const wxApiUrl = "https://api.weixin.qq.com/sns/jscode2session";
    const url = `${wxApiUrl}?appid=${appId}&secret=${appSecret}&js_code=${code}&grant_type=authorization_code`;

    const result = await uniCloud.httpclient.request(url, {
      method: "GET",
      dataType: "json",
    });

    if (result.data.errcode) {
      return {
        success: false,
        message: `微信登录失败: ${result.data.errmsg}`,
      };
    }

    return {
      success: true,
      data: {
        openId: result.data.openid,
        sessionKey: result.data.session_key,
        unionId: result.data.unionid,
      },
    };
  } catch (error) {
    console.error("获取微信openId失败:", error);
    return {
      success: false,
      message: "微信服务异常",
    };
  }
}

// 私有方法：验证token并获取用户信息
async function _verifyTokenAndGetUser(token, usersCollection) {
  const verifyResult = verifyToken(token);
  if (!verifyResult.valid) {
    return {
      success: false,
      code: 401,
      message: "Token无效，请重新登录"
    };
  }

  const userRes = await usersCollection
    .where({ wx_openid: verifyResult.payload.openId })
    .get();

  if (userRes.data.length === 0) {
    return {
      success: false,
      code: 404,
      message: "用户不存在"
    };
  }

  return {
    success: true,
    data: {
      openId: verifyResult.payload.openId,
      userInfo: userRes.data[0]
    }
  };
}

// 私有方法：同步用户信息到房间
async function syncUserInfoToRoom(userId, roomId, userInfo, roomsCollection) {
  const startTime = Date.now();
  
  try {
    console.log(`[SYNC_START] 开始同步用户信息 - UserId: ${userId}, RoomId: ${roomId}`);
    
    if (!roomId || !userId || !userInfo) {
      console.error(`[SYNC_ERROR] 参数不完整 - UserId: ${userId}, RoomId: ${roomId}, UserInfo: ${JSON.stringify(userInfo)}`);
      return {
        success: false,
        message: '参数不完整'
      };
    }

    // 获取房间信息
    const roomRes = await roomsCollection.doc(roomId).get();
    if (roomRes.data.length === 0) {
      console.error(`[SYNC_ERROR] 房间不存在 - RoomId: ${roomId}`);
      return {
        success: false,
        message: '房间不存在'
      };
    }

    const room = roomRes.data[0];
    console.log(`[SYNC_INFO] 找到房间 - RoomId: ${roomId}, Players: ${room.players.length}`);
    
    // 查找并更新用户在players数组中的信息
    const originalPlayers = [...room.players];
    const updatedPlayers = room.players.map(player => {
      if (player.user_id === userId) {
        const oldInfo = { nickname: player.nickname, avatar_fileId: player.avatar_fileId };
        const newInfo = {
          nickname: userInfo.nickname || player.nickname,
          avatar_fileId: userInfo.avatar_fileId || player.avatar_fileId
        };
        
        console.log(`[SYNC_INFO] 更新玩家信息 - UserId: ${userId}, Old: ${JSON.stringify(oldInfo)}, New: ${JSON.stringify(newInfo)}`);
        
        return {
          ...player,
          nickname: newInfo.nickname,
          avatar_fileId: newInfo.avatar_fileId
        };
      }
      return player;
    });

    // 检查用户是否在房间中
    const userInRoom = updatedPlayers.some(player => player.user_id === userId && player.has_left !== true);
    if (!userInRoom) {
      console.error(`[SYNC_ERROR] 用户不在房间中 - UserId: ${userId}, RoomId: ${roomId}`);
      return {
        success: false,
        message: '用户不在此房间中'
      };
    }

    // 检查是否有实际变更
    const hasChanges = JSON.stringify(originalPlayers) !== JSON.stringify(updatedPlayers);
    if (!hasChanges) {
      console.log(`[SYNC_INFO] 无需更新，信息已是最新 - UserId: ${userId}, RoomId: ${roomId}`);
      return {
        success: true,
        message: '用户信息已是最新，无需更新'
      };
    }

    // 更新房间信息
    const updateResult = await roomsCollection.doc(roomId).update({
      players: updatedPlayers
    });

    const duration = Date.now() - startTime;
    console.log(`[SYNC_SUCCESS] 用户信息同步成功 - UserId: ${userId}, RoomId: ${roomId}, Duration: ${duration}ms, Updated: ${updateResult.updated}`);

    return {
      success: true,
      message: '用户信息同步成功',
      duration: duration,
      updated: updateResult.updated
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[SYNC_FAILED] 同步用户信息到房间失败 - UserId: ${userId}, RoomId: ${roomId}, Duration: ${duration}ms, Error:`, error);
    
    return {
      success: false,
      message: error.message,
      duration: duration,
      error: error.name
    };
  }
}

// 私有方法：更新用户游戏统计（内部调用）
async function updateUserGameStats(userId, roomId, finalScore, db) {
  try {
    if (!userId || !roomId || finalScore === undefined) {
      return {
        code: 400,
        message: "参数不完整",
      };
    }

    console.log(`开始更新用户 ${userId} 在房间 ${roomId} 的统计数据，最终分数: ${finalScore}`);

    // 判定游戏结果
    let gameResult;
    let winIncrement = 0;
    let loseIncrement = 0;

    if (finalScore > 0) {
      gameResult = "win";
      winIncrement = 1;
    } else if (finalScore < 0) {
      gameResult = "lose";
      loseIncrement = 1;
    } else {
      gameResult = "draw";
    }

    // 使用事务确保数据一致性
    const transaction = await db.startTransaction();
    try {
      // 1. 检查是否存在参与记录，如果不存在则创建
      const participationCollection = db.collection("user_room_participations");
      const existingRecord = await participationCollection
        .where({
          user_id: userId,
          room_id: roomId,
        })
        .get();

      if (existingRecord.data.length === 0) {
        // 没有参与记录，创建一个
        console.log(`用户 ${userId} 在房间 ${roomId} 没有参与记录，创建新记录`);
        await transaction.collection("user_room_participations").add({
          user_id: userId,
          room_id: roomId,
          first_transaction_time: new Date(),
          final_score: finalScore,
          game_result: gameResult,
          is_settled: true,
          create_time: new Date(),
          update_time: new Date(),
        });
      } else {
        // 更新现有参与记录
        console.log(`用户 ${userId} 在房间 ${roomId} 已有参与记录，更新记录`);
        await transaction
          .collection("user_room_participations")
          .where({
            user_id: userId,
            room_id: roomId,
          })
          .update({
            final_score: finalScore,
            game_result: gameResult,
            is_settled: true,
            update_time: new Date(),
          });
      }

      // 2. 更新用户统计
      const updateData = {
        total_games: db.command.inc(1),
        participated_rooms: db.command.inc(1), // 只有在游戏结算时才计入参与房间数
      };

      if (winIncrement > 0) {
        updateData.win_games = db.command.inc(winIncrement);
      }
      if (loseIncrement > 0) {
        updateData.lose_games = db.command.inc(loseIncrement);
      }

      console.log(`更新用户 ${userId} 的统计数据:`, updateData);
      await transaction.collection("users").doc(userId).update(updateData);

      // 提交事务
      await transaction.commit();
      console.log(`用户 ${userId} 统计更新成功`);
    } catch (transactionError) {
      // 回滚事务
      await transaction.rollback();
      throw transactionError;
    }

    return {
      code: 200,
      message: "用户游戏统计更新成功",
      data: {
        userId,
        roomId,
        finalScore,
        gameResult,
      },
    };
  } catch (error) {
    console.error("更新用户游戏统计失败:", error);
    return {
      code: 500,
      message: "更新用户游戏统计服务异常",
    };
  }
}

module.exports = {
  // 云对象初始化
  _before: function () {
    this.db = uniCloud.database();
    this.usersCollection = this.db.collection("users");
    this.participationsCollection = this.db.collection("user_room_participations");
    this.appId = "wx02b0f59c159c575e";
    this.appSecret = "95490954cef249c2062603a76b06e83c";
  },

  /**
   * 微信小程序登录
   * @param {string} code 微信登录临时code
   * @returns {Object} 登录结果
   */
  async login(code) {
    try {
      if (!code) {
        return {
          code: 400,
          message: "登录code不能为空",
        };
      }

      // 1. 通过code获取微信用户openId
      const wxLoginResult = await getWxOpenId(code, this.appId, this.appSecret);
      if (!wxLoginResult.success) {
        return {
          code: 400,
          message: wxLoginResult.message,
        };
      }

      const { openId } = wxLoginResult.data;

      // 2. 查询用户是否存在
      const userRes = await this.usersCollection
        .where({ wx_openid: openId })
        .get();

      let userInfo;
      let isNewUser = false;

      if (userRes.data.length > 0) {
        // 老用户，直接获取用户信息
        userInfo = userRes.data[0];

        // 更新最后登录时间
        await this.usersCollection.doc(userInfo._id).update({
          last_login_time: new Date(),
          login_count: this.db.command.inc(1),
        });
      } else {
        // 新用户，创建用户记录
        const newUser = {
          wx_openid: openId,
          nickname: `用户${Date.now().toString().slice(-4)}`, // 默认昵称
          signature: "",
          gender: 0,
          total_games: 0,
          win_games: 0,
          lose_games: 0,
          created_rooms: 0,
          participated_rooms: 0, // 新增参与房间数字段
          user_level: 1,
          total_score: 0,
          status: 0,
          register_time: new Date(),
          last_login_time: new Date(),
          login_count: 1,
        };

        const createRes = await this.usersCollection.add(newUser);
        userInfo = {
          _id: createRes.id,
          ...newUser,
        };
        isNewUser = true;
      }

      // 3. 生成JWT token
      const token = generateToken(openId);

      return {
        code: 200,
        message: "登录成功",
        data: {
          token,
          userInfo,
          isNewUser, // 标识是否为新用户
          expiresIn: expiresIn, // 7天（秒）
        },
      };
    } catch (error) {
      console.error("登录失败:", error);
      return {
        code: 500,
        message: "登录服务异常",
      };
    }
  },

  /**
   * 刷新token
   * @param {string} token 当前token
   * @returns {Object} 刷新结果
   */
  async refreshToken(token) {
    try {
      // 验证当前token
      const verifyResult = verifyToken(token);
      if (!verifyResult.valid) {
        return {
          code: 401,
          message: "Token无效，请重新登录",
        };
      }

      // 生成新token
      const newToken = generateToken(verifyResult.payload.openId);

      return {
        code: 200,
        message: "Token刷新成功",
        data: {
          token: newToken,
          expiresIn: expiresIn,
        },
      };
    } catch (error) {
      console.error("Token刷新失败:", error);
      return {
        code: 500,
        message: "Token刷新服务异常",
      };
    }
  },

  /**
   * 获取用户信息
   * @param {string} token JWT token
   * @returns {Object} 用户信息
   */
  async getUserInfo(token) {
    try {
      // 验证token并获取用户信息
      const userResult = await _verifyTokenAndGetUser(token, this.usersCollection);
      if (!userResult.success) {
        return {
          code: userResult.code,
          message: userResult.message,
        };
      }

      const { userInfo } = userResult.data;

      return {
        code: 200,
        message: "获取用户信息成功",
        data: {
          _id: userInfo._id,
          wx_openid: userInfo.wx_openid,
          nickname: userInfo.nickname,
          avatar_fileId: userInfo.avatar_fileId,
          signature: userInfo.signature,
          gender: userInfo.gender,
          total_games: userInfo.total_games || 0,
          win_games: userInfo.win_games || 0,
          lose_games: userInfo.lose_games || 0,
          created_rooms: userInfo.created_rooms || 0,
          participated_rooms: userInfo.participated_rooms || 0, // 新增参与房间数字段
          user_level: userInfo.user_level || 1,
          total_score: userInfo.total_score || 0,
          register_time: userInfo.register_time,
          last_login_time: userInfo.last_login_time,
          login_count: userInfo.login_count || 0,
        },
      };
    } catch (error) {
      console.error("获取用户信息失败:", error);
      return {
        code: 500,
        message: "获取用户信息服务异常",
      };
    }
  },

  /**
   * 更新用户信息
   * @param {string} token JWT token
   * @param {Object} userInfo 用户信息
   * @returns {Object} 更新结果
   */
  async updateUserInfo(token, userInfo) {
    try {
      // 验证token并获取用户信息
      const userResult = await _verifyTokenAndGetUser(token, this.usersCollection);
      if (!userResult.success) {
        return {
          code: userResult.code,
          message: userResult.message,
        };
      }

      const { openId } = userResult.data;

      // 验证用户信息
      const updateData = {};
      if (userInfo.nickname !== undefined) {
        if (!userInfo.nickname || userInfo.nickname.length > 20) {
          return {
            code: 400,
            message: "昵称长度必须在1-20个字符之间",
          };
        }
        updateData.nickname = userInfo.nickname;
      }

      if (userInfo.avatar_fileId !== undefined) {
        updateData.avatar_fileId = userInfo.avatar_fileId;
      }

      if (userInfo.signature !== undefined) {
        if (userInfo.signature.length > 100) {
          return {
            code: 400,
            message: "个性签名不能超过100个字符",
          };
        }
        updateData.signature = userInfo.signature;
      }

      if (userInfo.gender !== undefined) {
        if (![0, 1, 2].includes(userInfo.gender)) {
          return {
            code: 400,
            message: "性别参数错误",
          };
        }
        updateData.gender = userInfo.gender;
      }

      if (Object.keys(updateData).length === 0) {
        return {
          code: 400,
          message: "没有需要更新的信息",
        };
      }

      // 更新用户信息
      const updateRes = await this.usersCollection
        .where({ wx_openid: openId })
        .update(updateData);

      if (updateRes.updated === 0) {
        return {
          code: 404,
          message: "用户不存在",
        };
      }

      // 获取更新后的完整用户信息
      const updatedUserRes = await this.usersCollection
        .where({ wx_openid: openId })
        .get();

      if (updatedUserRes.data.length === 0) {
        return {
          code: 404,
          message: "获取更新后用户信息失败",
        };
      }
      const updatedUserInfo = updatedUserRes.data[0];

      // 同步用户信息到活跃房间
      if (updatedUserInfo.active_room_id) {
        const syncStartTime = Date.now();
        try {
          const roomsCollection = this.db.collection("rooms");
          const syncResult = await syncUserInfoToRoom(
            updatedUserInfo._id, 
            updatedUserInfo.active_room_id, 
            {
              nickname: updatedUserInfo.nickname,
              avatar_fileId: updatedUserInfo.avatar_fileId
            }, 
            roomsCollection
          );
          
          const syncDuration = Date.now() - syncStartTime;
          
          if (!syncResult.success) {
            console.warn(`[SYNC_MONITOR] 同步失败 - UserId: ${updatedUserInfo._id}, Duration: ${syncDuration}ms, Error: ${syncResult.message}`);
            // 记录同步失败指标
            // 可以在这里接入监控服务，如腾讯云监控等
          } else {
            console.log(`[SYNC_MONITOR] 同步成功 - UserId: ${updatedUserInfo._id}, Duration: ${syncDuration}ms`);
            // 记录同步成功指标
          }
        } catch (syncError) {
          const syncDuration = Date.now() - syncStartTime;
          console.error(`[SYNC_MONITOR] 同步异常 - UserId: ${updatedUserInfo._id}, Duration: ${syncDuration}ms, Error:`, syncError);
          // 记录同步异常指标
        }
      }

      return {
        code: 200,
        message: "用户信息更新成功",
        data: updatedUserInfo,
      };
    } catch (error) {
      console.error("更新用户信息失败:", error);
      return {
        code: 500,
        message: "更新用户信息服务异常",
      };
    }
  },

  /**
   * 标记用户参与房间（首次有流水时调用）
   * @param {string} userId 用户ID
   * @param {string} roomId 房间ID
   * @returns {Object} 标记结果
   */
  async markUserRoomParticipation(userId, roomId) {
    try {
      if (!userId || !roomId) {
        return {
          code: 400,
          message: "参数不完整",
        };
      }

      // 检查是否已经标记过参与
      const existingRecord = await this.participationsCollection
        .where({
          user_id: userId,
          room_id: roomId,
        })
        .get();

      if (existingRecord.data.length > 0) {
        // 已经标记过，不需要重复操作
        return {
          code: 200,
          message: "用户已标记参与该房间",
          data: { alreadyExists: true },
        };
      }

      // 使用事务确保数据一致性
      const transaction = await this.db.startTransaction();
      try {
        // 1. 添加参与记录
        await transaction.collection("user_room_participations").add({
          user_id: userId,
          room_id: roomId,
          first_transaction_time: new Date(),
          final_score: 0,
          is_settled: false,
          game_result: "unfinished",
        });

        // 注意：不在此处递增 participated_rooms，而是在游戏真正结算时才计入统计
        
        // 提交事务
        await transaction.commit();
      } catch (transactionError) {
        // 回滚事务
        await transaction.rollback();
        throw transactionError;
      }

      return {
        code: 200,
        message: "用户参与房间标记成功",
        data: { isNewParticipation: true },
      };
    } catch (error) {
      console.error("标记用户参与房间失败:", error);
      return {
        code: 500,
        message: "标记用户参与房间服务异常",
      };
    }
  },

  /**
   * 批量更新房间玩家统计
   * @param {string} roomId 房间ID
   * @param {Array} playerScores 玩家分数列表 [{userId, finalScore}]
   * @returns {Object} 更新结果
   */
  async batchUpdateRoomPlayersStats(roomId, playerScores) {
    try {
      if (!roomId || !Array.isArray(playerScores)) {
        return {
          code: 400,
          message: "参数不完整",
        };
      }

      console.log(`开始批量更新房间 ${roomId} 的玩家统计，共 ${playerScores.length} 个玩家:`, playerScores);

      const results = [];
      const errors = [];

      // 批量处理每个玩家的统计
      for (const playerScore of playerScores) {
        try {
          console.log(`处理玩家 ${playerScore.userId}，分数: ${playerScore.finalScore}`);
          const result = await updateUserGameStats(
            playerScore.userId,
            roomId,
            playerScore.finalScore,
            this.db
          );
          results.push(result);

          if (result.code === 200) {
            console.log(`玩家 ${playerScore.userId} 统计更新成功`);
          } else {
            console.warn(`玩家 ${playerScore.userId} 统计更新失败:`, result.message);
          }
        } catch (error) {
          console.error(`更新玩家${playerScore.userId}统计异常:`, error);
          errors.push({
            userId: playerScore.userId,
            error: error.message,
          });
        }
      }

      const successCount = results.filter((r) => r.code === 200).length;
      console.log(`房间 ${roomId} 玩家统计批量更新完成: 成功 ${successCount}/${playerScores.length}, 失败 ${errors.length}`);

      return {
        code: 200,
        message: "批量更新房间玩家统计完成",
        data: {
          roomId,
          totalPlayers: playerScores.length,
          successCount,
          errorCount: errors.length,
          results,
          errors,
        },
      };
    } catch (error) {
      console.error("批量更新房间玩家统计失败:", error);
      return {
        code: 500,
        message: "批量更新房间玩家统计服务异常",
      };
    }
  },

  /**
   * 获取用户历史记录
   * @param {string} token JWT token
   * @param {number} page 页码，默认1
   * @param {number} pageSize 每页数量，默认20
   * @returns {Object} 用户历史记录和统计数据
   */
  async getUserHistory(token, page = 1, pageSize = 20) {
    try {
      // 验证token并获取用户信息
      const userResult = await _verifyTokenAndGetUser(token, this.usersCollection);
      if (!userResult.success) {
        return {
          code: userResult.code,
          message: userResult.message,
        };
      }

      const { userInfo } = userResult.data;

      // 参数验证
      if (page < 1) page = 1;
      if (pageSize < 1 || pageSize > 50) pageSize = 20;

      // 计算跳过的记录数
      const skip = (page - 1) * pageSize;

      // 1. 获取用户统计数据
      const totalGames = userInfo.total_games || 0;
      const wins = userInfo.win_games || 0;
      const losses = userInfo.lose_games || 0;
      const winRate = totalGames > 0 ? ((wins / totalGames) * 100).toFixed(2) + '%' : '0%';

      const stats = {
        totalGames,
        wins,
        losses,
        winRate
      };

      // 2. 聚合查询用户历史记录
      const historyResult = await this.db.collection('user_room_participations')
        .aggregate()
        .match({ 
          user_id: userInfo._id, 
          is_settled: true 
        })
        .lookup({
          from: 'rooms',
          localField: 'room_id',
          foreignField: 'room_id',
          as: 'room_info'
        })
        .unwind('$room_info')
        .sort({ 
          create_time: -1 
        })
        .skip(skip)
        .limit(pageSize)
        .project({
          _id: 1,
          room_id: 1,
          final_score: 1,
          game_result: 1,
          first_transaction_time: 1,
          create_time: 1,
          'room_info.room_name': 1,
          'room_info.create_time': 1,
          'room_info.start_time': 1
        })
        .end();

      // 3. 获取总记录数用于分页
      const countResult = await this.participationsCollection
        .where({
          user_id: userInfo._id,
          is_settled: true
        })
        .count();

      const total = countResult.total;
      const hasMore = skip + pageSize < total;
        console.log(historyResult,'historyResult')
      // 4. 格式化历史记录数据
      const historyList = historyResult.data.map(item => {
        // 使用房间的开始时间或创建时间作为游戏时间
        const gameTime = item['room_info.start_time'] || item['room_info.create_time'];
        const date = new Date(gameTime);
        
        return {
          id: item._id,
          roomName: item['room_info.room_name'],
          date: String(date.getMonth() + 1).padStart(2, '0') + '-' + String(date.getDate()).padStart(2, '0'),
          time: String(date.getHours()).padStart(2, '0') + ':' + String(date.getMinutes()).padStart(2, '0'),
          result: item.game_result === 'win' ? 'win' : (item.game_result === 'lose' ? 'loss' : 'draw'),
          amount: item.final_score || 0,
          roomId: item.room_id,
        };
      });

      return {
        code: 200,
        message: "获取用户历史记录成功",
        data: {
          stats,
          historyList,
          pagination: {
            currentPage: page,
            pageSize,
            total,
            hasMore
          }
        },
      };
    } catch (error) {
      console.error("获取用户历史记录失败:", error);
      return {
        code: 500,
        message: "获取用户历史记录服务异常",
      };
    }
  },

  /**
   * 修复房间参与记录和统计数据
   * @param {string} roomId 房间ID
   * @returns {Object} 修复结果
   */
  async repairRoomParticipationStats(roomId) {
    try {
      if (!roomId) {
        return {
          code: 400,
          message: "房间ID不能为空",
        };
      }

      console.log(`开始修复房间 ${roomId} 的参与记录和统计数据`);

      // 1. 获取房间信息
      const roomsCollection = this.db.collection("rooms");
      const roomRes = await roomsCollection.where({ room_id: roomId }).get();

      if (roomRes.data.length === 0) {
        return {
          code: 404,
          message: "房间不存在",
        };
      }

      const room = roomRes.data[0];

      // 2. 检查房间是否已结束
      if (room.room_status !== "finished") {
        return {
          code: 400,
          message: "只能修复已结束的房间",
        };
      }

      // 3. 计算所有玩家的最终分数
      const messagesCollection = this.db.collection("room_messages");
      const { parseSettlementAdjustments } = require('./settlement-utils');

      // 重新计算分数（复用现有逻辑）
      const calculationResult = await this._calculatePlayerFinalScores(
        roomId,
        room.players,
        messagesCollection,
        this.db,
        room.tea_water_balance || 0
      );

      const playerScores = calculationResult.playerScores;

      // 4. 确保所有房间玩家都有分数记录
      const allParticipants = room.players.map(player => ({
        userId: player.user_id,
        finalScore: 0
      }));

      // 更新有实际分数的玩家
      playerScores.forEach(scoreData => {
        const participant = allParticipants.find(p => p.userId === scoreData.userId);
        if (participant) {
          participant.finalScore = scoreData.finalScore;
        }
      });

      // 5. 批量修复统计数据
      const repairResult = await this.batchUpdateRoomPlayersStats(roomId, allParticipants);

      return {
        code: 200,
        message: "房间参与记录和统计数据修复完成",
        data: {
          roomId,
          roomStatus: room.room_status,
          totalPlayers: allParticipants.length,
          repairResult: repairResult.data,
        },
      };
    } catch (error) {
      console.error("修复房间参与记录失败:", error);
      return {
        code: 500,
        message: "修复房间参与记录服务异常",
      };
    }
  },

  /**
   * 私有方法：计算玩家最终分数（从room模块复制）
   */
  async _calculatePlayerFinalScores(roomId, players, messagesCollection, db, initialTeaBalance = 0) {
    // 这里应该复用room模块的计算逻辑，为了简化，我们先返回基础结构
    // 实际使用时需要完整实现或调用room模块的方法
    const playerScores = [];
    players.forEach((player) => {
      playerScores.push({
        userId: player.user_id,
        finalScore: 0, // 简化处理，实际应该计算真实分数
      });
    });

    return {
      playerScores: playerScores,
      calculatedTeaBalance: initialTeaBalance,
    };
  },
};
